<template>
  <view class="cardimg" :style="{ backgroundImage: `url(${bg})` }">
    <div class="label">{{ data[labelIndex].label }}</div>
    <div class="progress">
      <div class="inside" :style="{ width: `${progressWidth}%` }"></div>
    </div>
  </view>
</template>

<script lang="ts" setup>
import { onHide, onShow } from '@dcloudio/uni-app'
import { Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { check_token } from '@/api/index'
import bg from '@/static/image/login/loading.png'

const { t } = useI18n()

const labelIndex = ref(0)
const progressWidth = ref(0)
const data = ref([
  { label: `${t('indexLoading.label1')}`, show: false },
  { label: `${t('indexLoading.label2')}`, show: false },
  { label: `${t('indexLoading.label3')}`, show: false },
  { label: `${t('indexLoading.label4')}`, show: false },
  { label: `${t('indexLoading.label5')}`, show: false },
  { label: `${t('indexLoading.label6')}`, show: false }
])

onShow(() => {
  checkToken()
  startTimer()
  progressWidth.value = 0
  labelIndex.value = 0
})
onHide(() => {
  clearInterval(timer.value)
})
let islogin = false
const checkToken = async () => {
  const res = await check_token()
  if (res.code === 1) {
    islogin = res.data.login
  }
}

const timer: Ref<any> = ref(null)
const startTimer = () => {
  let index = 0
  timer.value = setInterval(() => {
    console.log(labelIndex.value)
    console.log(data.value[labelIndex.value].label)
    if (index >= data.value.length - 1) {
      clearInterval(timer.value)
      if (!islogin) {
        uni.navigateTo({
          url: '/subPackages/login/login'
        })
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
      return
    }
    progressWidth.value += 100 / 5
    labelIndex.value += 1
    refreshData(index++)
  }, 800)
}
const refreshData = (index: any) => {
  data.value[index].show = true
}
</script>

<style lang="scss">
.progress {
  width: 90%;
  height: 0.5rem;
  border-radius: 0.31rem;
  background: #eee;
  position: fixed;
  bottom: calc(var(--vh) * 10);
  .inside {
    height: 0.5rem;
    border-radius: 0.31rem;
    background: $color-primary;
    width: 0;
    position: absolute;
    top: 0;
    left: 0;
    transition: all 1.7s;
  }
}
.label {
  position: fixed;
  bottom: calc(var(--vh) * 12);
  color: #141414;
  text-align: center;
}
.cardimg {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  min-height: calc(var(--vh) * 100);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
</style>
