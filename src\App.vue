<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useCounterStore } from '@/store/store'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
const store = useCounterStore()
// store.getSystemInfo()
// store.getUserInfo()

const i = 'orientationchange' in window ? 'orientationchange' : 'resize'
const setViewHeight = () => {
  const windowVH = window.innerHeight / 100
  document.documentElement.style.setProperty('--vh', windowVH + 'px')
}
document.addEventListener('DOMContentLoaded', setViewHeight)
window.addEventListener(i, setViewHeight)

onLaunch(() => {
  store.token = uni.getStorageSync('token') || null
  store.userId = uni.getStorageSync('userId') || null
  store.userInfo = uni.getStorageSync('userInfo') || null

  // show()
  const router = useRouter()
  router.beforeEach((to, from, next) => {
    // hide(next)
    next()
  })
  router.afterEach(() => {
    // setTimeout(() => show(), 50)
  })
})
onMounted(() => {
  store.pageHeight = document.body.clientHeight + 'px'
  document.body.style.height = document.body.clientHeight + 'px'
})
onShow(() => {})
onHide(() => {})
</script>
<style lang="scss">
html,
body {
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch; /* 启用惯性滚动 */
}
.hh2 {
  display: -webkit-box;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.hh {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  word-break: break-all;
}
.uni-input-input {
  color: $color-black;
}
.tabbar {
  border-radius: 0.625rem 0.625rem 0rem 0rem;
  // box-shadow: 0.00rem 0.00rem 0.31rem 0.13rem #f4f4f4;
}
body {
  background: #fafafa;
}
::v-deep .uni-toast__icon {
  margin: 0.625rem auto 0 !important;
}

::v-deep .van-loading__line:before {
  color: #000 !important;
  background: #000 !important;
}

.uni-modal {
  padding: 0 1.25rem 1.25rem 1.25rem;
  border-radius: 0.625rem;
  .uni-modal__ft:after {
    display: none;
  }
  .uni-modal__btn:after {
    display: none;
  }
  .uni-modal__btn_default {
    width: 8.4375rem;
    height: 3.0625rem;
    border-radius: 1.5625rem;
    background: #08182b;
    color: #fff !important;
    font-size: 1rem;
    margin-right: 0.625rem;
  }
  .uni-modal__btn_primary {
    width: 8.4375rem;
    height: 3.0625rem;
    border-radius: 1.5625rem;
    background: #c9c8c9;
    font-size: 1rem;
    color: #fff !important;
  }
}

.up {
  color: $uni-text-color-red;
}
.down {
  color: $uni-text-color-green;
}
.ping {
  color: $uni-text-color;
}
.red {
  color: $color-red !important;
}
.green {
  color: $color-green !important;
}
.rotate {
  transform: rotate(180deg);
}
.nodata {
  font-size: $uni-font-size-lg;
  text-align: center;
  margin-top: 25.38rem;
}
uni-tabbar {
  display: none;
}

/* #ifdef H5 */
uni-page {
  opacity: 1;
  transition: all 0.3s ease;
}

uni-page.animation-before {
  /* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
  transform: translateY(0.625rem);
  opacity: 0;
}

uni-page.animation-leave {
  transition: all 0.3s ease;
}

uni-page.animation-enter {
  transition: all 0.3s ease;
}

uni-page.animation-show {
  opacity: 1;
}

uni-page.animation-after {
  /* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
  transform: translateY(0);
}

/* #endif */

.input_wrap {
  display: flex;
  align-items: center;
  height: 3.06rem;
  border-radius: 0.75rem;
  margin-bottom: 0.31rem;
  background: #e5ebf2;
  padding: 0 0.88rem;
  max-width: 20rem;
  margin-left: auto;
  margin-right: auto;
  font-size: 1rem;
  gap: 1rem;
  input {
    flex: 1;
    text-align: left;
    .uni-input-placeholder {
      color: $color-gray2;
    }
  }
  .pwd {
    padding: 0.2rem;
  }
  img {
    width: 1.5rem;
    height: 1.5rem;
    object-fit: contain;
  }
  .icon {
    width: 0.94rem;
    height: 1.06rem;
  }
}
</style>
