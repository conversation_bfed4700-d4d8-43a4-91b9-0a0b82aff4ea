<template>
  <view class="container">
    <view class="wrap">
      <view class="title">{{ t('register.tip4') }}</view>
      <view class="mas">
        <view class="input_wrap" @click="phoneFocus = true">
          <img class="icon" :src="phone" alt="" />
          <input v-model="registerParams.account" :placeholder="t('register.account_placeholder')" :focus="phoneFocus" autocomplete="false" @blur="phoneFocus = false" />
        </view>
        <view class="input_wrap" @click="passwordFocus = true">
          <img class="icon" :src="password" alt="" />
          <input
            v-model="registerParams.password"
            :placeholder="t('register.password_placeholder')"
            :type="showPassword ? 'text' : 'password'"
            :focus="passwordFocus"
            autocomplete="false"
            @blur="passwordFocus = false"
          />
          <img class="pwd" :src="showPassword ? eye : eye_close" alt="" @click.stop="showPassword = !showPassword" />
        </view>
        <view class="input_wrap" @click="rePasswordFocus = true">
          <img class="icon" :src="password" alt="" />
          <input
            v-model="registerParams.again_password"
            :placeholder="t('register.password_again_placeholder')"
            :focus="rePasswordFocus"
            autocomplete="false"
            :type="showRePassword ? 'text' : 'password'"
            @blur="rePasswordFocus = false"
          />
          <img class="pwd" :src="showRePassword ? eye : eye_close" alt="" @click.stop="showRePassword = !showRePassword" />
        </view>
        <view class="input_wrap" @click="codeFocus = true">
          <img class="icon" :src="code" alt="" />
          <input v-model="registerParams.yaoqingma" :placeholder="t('register.code_placeholder')" :focus="codeFocus" @blur="codeFocus = false" />
        </view>
      </view>
      <view class="button-wrap">
        <view class="button" @click="register">{{ t('register.tip4') }}</view>
        <view class="button circle_button" @click="reLaunch('/subPackages/login/login')">{{ t('login.button_text') }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { registerApi } from '@/api/login/login'
import { reLaunch, checkInput, switchTab } from '@/common/common'
import { showFailToast, showLoadingToast, showSuccessToast, showToast } from 'vant'
import { useCounterStore } from '@/store/store'
import eye from '@/static/image/login/eye.png'
import eye_close from '@/static/image/login/eye-close.png'
import phone from '@/static/image/login/phone.png'
import password from '@/static/image/login/password.png'
import code from '@/static/image/login/code.png'
const { t } = useI18n()
const store = useCounterStore()

const registerParams = ref({
  account: '',
  password: '',
  again_password: '',
  yaoqingma: ''
})

const showPassword = ref(false)
const showRePassword = ref(false)

const phoneFocus = ref(false)
const passwordFocus = ref(false)
const rePasswordFocus = ref(false)
const codeFocus = ref(false)

const checkArr = [
  { key: 'account', message: t('register.account_error') },
  { key: 'password', message: t('register.password_error') },
  { key: 'again_password', message: t('register.password_again_error') },
  { key: 'yaoqingma', message: t('register.code_error') }
]

const register = async () => {
  const reg = /^(070|080|090)\d{8}$/
  if (!checkInput(checkArr, registerParams.value)) {
    return
  }
  if (!reg.test(registerParams.value.account)) {
    return showToast(t('register.tip1'))
  }

  if (registerParams.value.password.length < 6 || registerParams.value.password.length > 10) {
    return showToast(t('register.tip2'))
  }

  if (registerParams.value.password !== registerParams.value.again_password) {
    showFailToast(t('register.password_repeat_error'))
    return
  }
  showLoadingToast({ mask: true })
  const res = await registerApi(registerParams.value)
  // closeToast()
  if (res.code === 1) {
    uni.setStorageSync('token', res.data.userinfo.token)
    uni.setStorageSync('userId', res.data.userinfo.id)
    uni.setStorageSync('userInfo', res.data.userinfo)
    store.token = res.data.userinfo.token
    store.userId = res.data.userinfo.userId
    store.getUserInfo()
    showSuccessToast(res.msg)
    setTimeout(() => {
      switchTab('/pages/index/index')
    }, 1000)
  } else {
    showFailToast(res.msg)
  }
}
</script>

<style lang="scss" scoped>
.navigator {
  width: 100%;
  height: 3.13rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  .van-icon {
    transform: rotate(180deg);
  }
}
.button-wrap {
  width: 20rem;
  height: 3.125rem;
  justify-content: space-between;
  align-content: center;
  margin-top: 0.625rem;
  .text {
    color: #a6a6a6;
    font-size: 0.9375rem;
    line-height: 3.125rem;
  }
}
.mas {
  width: 20rem;
  border-radius: 1.25rem;
}
body {
  background: $color-white;
}
.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: calc(var(--vh) * 100);
}
.container {
  text-align: center;
  margin: 0 auto;
  .bg {
    width: 6.25rem;
    height: 6.25rem;
    margin: 2.25rem auto 0;
    border-radius: 0.63rem;
  }
  .title {
    color: $color-primary;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 2rem 0 4.38rem;
  }
}
.logo {
  width: 7.5rem;
  height: 7.5rem;
  margin: 14vh auto 0vh;
}
.name {
  margin: 0vh auto 9.54vh;
  font-size: 1.5rem;
  color: $uni-color-primary;
}
.button {
  width: 100%;
  height: 3.75rem;
  border-radius: 2.63rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: $color-white;
  background: $color-primary;
  font-size: 1.13rem;
  font-weight: 500;
}
.circle_button {
  border: 0.06rem solid $color-primary;
  background: transparent !important;
  color: $color-primary;
  margin-top: 1.25rem;
}
</style>
