<!DOCTYPE html>
<html lang="jp" translate="no">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />'
      )

      document.addEventListener('dblclick', function (e) {
        e.preventDefault() // 阻止默认双击事件
      })
    </script>
    <title>PGIMAX</title>
    <!--preload-links-->
    <!--app-context-->

    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="renderer" content="webkit" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <meta name="HandheldFriendly" content="true" />
    <meta name="screen-orientation" content="portrait" />
    <meta name="x5-orientation" content="portrait" />
    <meta name="full-screen" content="yes" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="browsermode" content="application" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-navbutton-color" content="#fff" />
    <meta name="msapplication-TileColor" content="#fff" />
    <meta name="theme-color" content="#fff" />
    <meta name="apple-mobile-web-app-title" content="PGIMAX" />
    <meta name="google" content="notranslate" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="./src/static/image/login/head.png" />

    <!-- Android Chrome Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="./src/static/image/login/head.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="./src/static/image/login/head.png" />

    <!-- Windows Tiles -->
    <meta name="msapplication-TileImage" content="./src/static/image/login/head.png" />
    <meta name="msapplication-TileColor" content="#ffffff" />

    <!-- General Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./src/static/image/login/head.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="./src/static/image/login/head.png" />
    <link rel="icon" type="image/x-icon" href="./src/static/image/login/head.png" />
    <link rel="icon" href="./src/static/image/login/head.png" />
  </head>

  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
