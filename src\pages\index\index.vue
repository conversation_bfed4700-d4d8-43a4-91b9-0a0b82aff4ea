<template>
  <view class="container">
    <!-- 检索栏 -->
    <view class="head">
      <img class="logo" src="/static/image/index/head.png" alt="" />
      <div class="right">
        <van-badge :dot="dot" style="display: flex">
          <image src="/static/image/index/message.png" class="xiaoxi" mode="widthFix" @click="goPage('/subPackages/message/message')"></image>
        </van-badge>
      </div>
    </view>

    <!-- 主体 -->
    <view class="content">
      <!-- 股票 -->
      <div class="card">
        <div class="zhishuList">
          <div
            v-for="(item, index) in zhishuList"
            :key="index"
            :class="{ green: item.zhangdieshu >= 0, red: item.zhangdieshu < 0 }"
            class="block"
            @click="goPage(`/subPackages/market/zhishuDetail/zhishuDetail?id=${item.id}`)"
          >
            <div class="title">{{ item.name }}</div>
            <div class="price">
              <span :class="getColor(item)">{{ item.price }}</span>
              <img :src="item.zhangdieshu >= 0 ? up : down" alt="" />
            </div>
            <div class="rate">
              <span :class="getColor(item)">{{ item.zhangdieshu }}</span>
              <span :class="getColor(item)">({{ item.zhangdiebaifenbi }}%)</span>
            </div>
          </div>
        </div>
        <div class="left">
          <div class="flex">
            <div class="price" :class="getColor(activeGupiao)">{{ activeGupiao.price }}</div>
            <div class="title" @click="openSelect">
              <span>{{ activeGupiao?.name || '' }}</span>
              <van-icon name="arrow-down" color="#fff"></van-icon>
            </div>
          </div>
          <div class="info">
            <div class="label" :class="getColor(activeGupiao)">{{ activeGupiao?.zhangdieshu }}</div>
            <div class="rate" :class="getColor(activeGupiao)">{{ `(${activeGupiao?.zhangdiebaifenbi})%` }}</div>
          </div>
        </div>
        <div class="right">
          <GupiaoChart :value="chartValue" height="16.25rem" />
        </div>

        <view class="buttonList">
          <view v-for="(item, index) in buttonList" :key="index" class="button" @click="goPage2(item)">
            <div class="image-box">
              <image :src="item.icon"></image>
            </div>
            <view class="text">{{ item.label }}</view>
          </view>
        </view>
      </div>

      <view class="scroll_list">
        <view class="title-top" @click="switchTab('/pages/market/market')">
          <view class="title h">{{ t('index.hot') }}</view>
          <view class="title-right">{{ t('index.tip2') }} <van-icon name="arrow"></van-icon></view>
        </view>
        <view class="gupiao_wrap">
          <view v-for="(item, index) in hotGupiaoList" :key="index" class="gupiao" :class="getColor(item)" @click="goPage(`/subPackages/index/gupiaoDetail/gupiaoDetail?id=${item.id}`)">
            <div class="title">{{ item.name }}</div>

            <div class="flex">
              <div class="left">
                <div class="row">
                  <div class="price">
                    <span :class="getColor(item)">{{ item.price }}</span>
                  </div>
                </div>

                <div class="row">
                  <div class="rate">
                    <span :class="getColor(item)">{{ item.zhangdieshu }}</span>
                    <span :class="getColor(item)">（{{ item.zhangdiebaifenbi }}%）</span>
                  </div>
                </div>
              </div>
              <div class="right">
                <img :class="item.zhangdiebaifenbi >= 0 ? 'up' : 'down'" :src="item.zhangdiebaifenbi >= 0 ? up2 : down2" alt="" />
              </div>
            </div>
          </view>
        </view>
      </view>
    </view>

    <van-popup v-model:show="selectShow" class="select_window" v-bind="windowOptions">
      <view v-for="(item, index) in hotGupiaoList.slice(0, 3)" :key="index" class="title" @click="handleSelect(index)">{{ item.name }}</view>
    </van-popup>
  </view>
  <CustomTabbar :id="0" />
</template>

<script lang="ts" setup>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'
import GupiaoChart from './components/gupiaoChart.vue'
import { useCounterStore } from '@/store/store'
import { getTickerKApi, getHotGupiaoApi } from '@/api/index/index'
import { getUserMessageApi } from '@/api/message'
import { onHide, onShow } from '@dcloudio/uni-app'
import { ref, watch, computed } from 'vue'
import { getCzhiurlApi } from '@/api/user'
import { useI18n } from 'vue-i18n'
import { goPage, getColor, switchTab } from '@/common/common'
import up from '@/static/image/index/up.png'
import down from '@/static/image/index/down.png'
import up2 from '@/static/image/market/up2.png'
import down2 from '@/static/image/market/down2.png'
import { getZhishuApi } from '@/api/market/market'
const { t } = useI18n()
// 页面基础配置
const store = useCounterStore()
console.log(store)
const dot = ref(false)
// 获取消息
const getUserMessage = async () => {
  const res = await getUserMessageApi()
  if (res.code === 1) {
    dot.value = res.data.data[0]?.is_read === '0'
  }
}

const kefu_url = ref('')

const getCzhiurlFn = async () => {
  const res = await getCzhiurlApi()
  if (res.code === 1) {
    kefu_url.value = res.data.kefu_url
  }
}

const goWeb = () => {
  window.open(kefu_url.value)
}

const goPage2 = (e) => {
  if (e.type === 'kefu') {
    goWeb()
  } else {
    goPage(e.url)
  }
}

let intervalId: ReturnType<typeof setInterval> | null = null
onShow(() => {
  getHotGupiao()
  getUserMessage()
  getCzhiurlFn()
  getZhishu()

  if (intervalId === null) {
    intervalId = setInterval(() => {
      getHotGupiao()
      getUserMessage()
    }, 10000)
  }
})

onHide(() => {
  console.log(intervalId, '定时器ID清除')
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = null
  }
})

const buttonList = [
  { label: t('index.button1'), icon: '/static/image/index/icon1.png', url: '/subPackages/index/ipo/ipo' },
  { label: t('hongli.title'), icon: '/static/image/index/icon2.png', url: '/subPackages/index/hongli/hongli' },
  { label: t('index.button4'), icon: '/static/image/index/icon3.png', url: '/subPackages/transactionLog/transactionLog?isLs=1' },
  { label: t('rongzi.title'), icon: '/static/image/index/icon4.png', url: '/subPackages/index/rongzi/rongzi' },
  { label: t('dadan.title'), icon: '/static/image/index/icon5.png', url: '/subPackages/index/dadan/dadan' },
  { label: t('kefu.title'), icon: '/static/image/index/icon6.png', type: 'kefu' }
]

const gupiaoActive = ref(0)
const chartValue = ref({})

// 熱門股票
interface hotGupiaoType {
  name: string
  shuzidaima: string
  price: string
  zhangdiebaifenbi: number | string
  zhangdieshu: number | string
  id: number
}
const hotGupiaoList = ref<Array<hotGupiaoType>>([])
const activeGupiao = computed(() => {
  return hotGupiaoList.value[gupiaoActive.value] || { zhangdiebaifenbi: 0 }
})
const getHotGupiao = async () => {
  const res = await getHotGupiaoApi()
  hotGupiaoList.value = res.data.result
}

/** **************************** 指数列表 **************************/
const zhishuList = ref([])
const getZhishu = async () => {
  const res = await getZhishuApi()
  zhishuList.value = res.data
}

// 股票chart
watch(
  () => hotGupiaoList,
  (newData) => {
    resetChartData(newData.value[gupiaoActive.value])
  },
  { deep: true }
)
const resetChartData = async (data: any) => {
  console.log(111, data)
  const params = {
    id: data.id,
    kline_type: 6,
    type: 1
  }
  const res = await getTickerKApi(params)
  const kLineList = res.data.map((item: any) => {
    return {
      close: item.c,
      high: item.h,
      low: item.l,
      open: item.o,
      timestamp: item.t * 1000,
      volume: item.v
    }
  })
  chartValue.value = kLineList
}

// 打开筛选框
const selectShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true
}
const openSelect = () => {
  selectShow.value = true
}
const handleSelect = (index: number) => {
  gupiaoActive.value = index
  resetChartData(hotGupiaoList.value[gupiaoActive.value])
  selectShow.value = false
}
</script>
<style scoped lang="scss">
.container {
  overflow: hidden;
  height: calc(var(--vh) * 100 - 4.375rem);
}

.h {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    width: 0%;
    height: 0.19rem;
    background: #0df69e;
    border-radius: 0.25rem;
    bottom: -0.31rem;
    opacity: 0;
    transition: all 1s;
  }
  &:hover {
    &::before {
      width: 100%;
      opacity: 1;
    }
  }
}

.search {
  width: 100%;
  height: 3.125rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem 0.625rem 0.9375rem;
  gap: 1.25rem;
  margin: 0.53rem 0;
  .touxian {
    width: 1.7813rem;
    height: 1.7813rem;
    border-radius: 50%;
    margin-right: 0.9375rem;
  }

  .search-box {
    width: 14.375rem;
    flex: 1;
    height: 2.75rem;
    border-radius: 1.25rem;
    background: #262329;
    border: 0.06rem solid #565358;
    box-shadow: 0rem 0.19rem 13.19rem 0rem rgba(0, 0, 0, 0.05);
    line-height: 2.75rem;
    padding: 0 0.94rem;
    font-size: 0.9375rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    .search-icon {
      width: 1rem;
      height: 1rem;
    }
    span {
      color: #aeaeae;
      font-size: 0.82rem;
    }
  }

  .kefu {
    width: 1rem;
    height: 1rem;
  }

  .xiaoxi {
    width: 2.5rem;
  }
  ::v-deep .van-badge {
    position: absolute;
    top: 0.7rem;
    right: 0.7rem;
  }
}

.head {
  height: 3.125rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.9375rem;
  gap: 1.25rem;
  margin: 0.53rem 0;
  .logo {
    width: 10.63rem;
  }
  .right {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    background: $color-primary;
    width: 2.75rem;
    height: 2.75rem;
    border-radius: 100%;
    justify-content: center;
    img {
      width: 1.25rem;
      height: 1.25rem;
    }
  }
  .xiaoxi {
    width: 1.25rem;
    height: 1.25rem;
  }
  ::v-deep .van-badge {
    position: absolute;
    top: 0.2rem;
    right: 0.2rem;
  }
}

.content {
  height: calc(var(--vh) * 100 - 4.38rem - 3.125rem - 1.06rem);
  overflow-y: auto;
  .card {
    margin: 0 0.5rem 0;
    border-radius: 0.94rem;
    padding: 1rem 0;
    > div {
      flex: 1;
    }

    .left {
      padding: 0 1rem;
      .flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .title {
        font-size: 0.81rem;
        font-weight: bold;
        background: $color-primary;
        border-radius: 0.38rem;
        padding: 0.13rem 0.31rem;
        span {
          color: $color-white;
          margin-right: 0.64rem;
        }
      }
      .price {
        font-size: 1.25rem;
        font-weight: bold;
      }
      .info {
        display: flex;
        gap: 0.31rem;
        align-items: center;
      }
      .rate {
        padding: 0.19rem;
        border-radius: 0.31rem;
        font-size: 0.81rem;
      }
      .label {
        font-size: 0.81rem;
      }
    }
    .right {
      padding: 0 1rem;
      padding: 1rem;
      border-radius: 1.5rem;
    }
  }

  .zhishuList {
    display: flex;
    justify-content: space-between;
    gap: 0.3rem;
    margin-bottom: 1rem;
    .block {
      flex: 1;
      border-radius: 0.63rem;
      padding: 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: #efefef;
      .title {
        color: #000;
        font-weight: 500;
        text-align: center;
        font-size: 0.81rem;
        max-width: 6rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0 auto;
      }
      .price {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.1rem;
        span {
          font-size: 0.88rem;
        }
        img {
          transform: rotate(180deg);
          width: 0.94rem;
          display: block;
        }
      }
      .rate {
        text-align: center;
        span {
          font-size: 0.69rem;
        }
      }
    }
  }

  .buttonList {
    margin: 1.25rem 0 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
    .button {
      width: calc((100% - 0.9rem) / 4);
      border-radius: 0.63rem;
      gap: 0.3rem;
      padding: 0.3rem 0.2rem;
    }
    .image-box {
      display: flex;
      justify-content: center;
      align-items: center;
      background: $color-primary;
      width: 3.56rem;
      height: 3.56rem;
      margin: 0 auto;
      border-radius: 50%;
    }
    image {
      width: 1.5rem;
      height: 1.5rem;
    }
    .text {
      color: $color-primary;
      font-size: 0.72rem;
      text-align: center;
      font-weight: 500;
      margin-top: 0.6rem;
      display: flex;
      justify-content: center;
      align-items: flex-start;
    }
  }

  .scroll_list {
    padding: 1rem 0;
    margin: 0 0.94rem;
    .gupiao_wrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 0.625rem;

      .gupiao {
        width: 10.4688rem;
        height: 6.1875rem;
        padding: 0.94rem;
        margin-bottom: 0.625rem;
        background: #efefef;
        border-radius: 1.5rem;

        .title {
          color: $color-black;
          font-size: 0.875rem;
          font-weight: 500;
          max-height: 6.25rem;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          word-break: break-all;
        }

        .flex {
          display: flex;
          align-items: center;
        }

        .price {
          font-size: 0.94rem;
          font-weight: 500;
          margin: 0.3rem 0 0.2rem;
        }
        .rate {
          font-size: 0.69rem;
          white-space: nowrap;
        }

        .right {
          .up {
            transform: rotateY(180deg);
          }
          .down {
            transform: rotateY(180deg);
            transform: rotateX(180deg);
          }
        }
      }

      .gupiao.down {
        background: #172f21;
        border: 0.05rem solid #959595;
      }

      .gupiao.up {
        background: #350f16;
        border: 0.05rem solid #959595;
      }

      .bottom {
        display: flex;
        justify-content: space-between;
        image {
          width: 2.94rem;
        }
      }
    }

    .title-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .title {
      color: $color-black;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .title-right {
      display: flex;
      align-items: center;
      color: $color-black;
      font-size: 0.875rem;
      font-weight: 400;
      img {
        height: 0.7383rem;
        width: 0.4375rem;
        margin-left: 0.4063rem;
      }
    }
  }

  .wrap {
    margin-top: 1.4063rem;
    > .title {
      height: 2.38rem;
      padding-top: 1.06rem;
      font-size: $uni-font-size-1;
      font-weight: bold;
      margin-left: 0.47rem;
    }
    .button_wrap {
      display: grid;
      grid-template-columns: 50% 50%;
      grid-template-rows: 2.69rem 2.69rem 2.69rem 2.69rem;
      row-gap: 0.25rem;
      column-gap: 0.44rem;
      .button {
        background-color: #638daa;
        border-radius: 0.56rem;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
          width: 0.94rem;
          height: 0.94rem;

          margin-right: 0.47rem;
        }
        view {
          color: #fff;
          width: 6.75rem;
          font-size: 0.63rem;
        }
      }
    }
  }
}
.select_window {
  padding: 0.51rem 1rem 0.9rem;
  .title {
    height: 2.5rem;
    line-height: 2.5rem;
    font-size: $uni-font-size-lg;
    padding: 0 1rem;
    text-align: center;
    border-bottom: 0.03rem solid #ccc;
  }
}
</style>
